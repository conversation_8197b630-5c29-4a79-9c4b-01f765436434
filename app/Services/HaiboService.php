<?php

namespace App\Services;

use App\Models\Merchant;
use App\Models\MerchantToken;
use App\Models\O2oErrandOrder;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;

class HaiboService
{
    protected string $appKey;
    protected string $appSecret;

    public function __construct()
    {
        // 海博平台的配置信息 - 这些需要根据实际的海博平台配置进行调整
        $this->appKey = "haibo_app_key";
        $this->appSecret = "haibo_app_secret";


    }

    /**
     * 创建或修改配送商门店
     *
     * @param array $data 门店数据
     * @return array 处理结果
     * @throws \Exception
     */
    public function createOrUpdateStore(array $data): array
    {
        Log::channel('haibo')->info('海博创建/修改配送商门店请求', $data);

        try {

            // 开始数据库事务
            DB::beginTransaction();

            // 检查是否已存在该门店
            $existingMerchant = $this->findExistingMerchant($data);

            if ($existingMerchant) {
                // 更新现有门店
                $result = $this->updateExistingStore($existingMerchant, $data);
            } else {
                // 创建新门店
                $result = $this->createNewStore($data);
            }

            DB::commit();

            Log::channel('haibo')->info('海博门店操作成功', [
                'operation' => $existingMerchant ? 'update' : 'create',
                'merchant_id' => $result['merchant_id'],
                'user_id' => $result['user_id']
            ]);

            return [
                'success' => true,
                'message' => $existingMerchant ? '门店更新成功' : '门店创建成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::channel('haibo')->error('海博门店操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            throw $e;
        }
    }


    /**
     * 查找已存在的商家
     *
     * @param array $data
     * @return Merchant|null
     */
    private function findExistingMerchant(array $data): ?Merchant
    {
        return Merchant::where('merchant_type', 'haibo')
            ->where('phone', $data['contactPhone'])
            ->first();
    }

    /**
     * 创建新门店
     *
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function createNewStore(array $data): array
    {
        // 检查该手机号是否已有用户账号
        $user = User::where('phone', $data['contactPhone'])->first();

        // 如果没有用户账号，则创建一个
        if (!$user) {
            $userService = app(UserService::class);
            $user = $userService->registerUser(
                $data['contactPhone'],
                Hash::make('123456'), // 默认密码
                '', // 空邀请码
                \App\Models\SystemConfig::PlatformPT // 平台标识为跑腿平台
            );

            Log::channel('haibo')->info("为海博商家创建关联用户账号成功", [
                'user_id' => $user->id,
                'phone' => $data['contactPhone']
            ]);
        }

        // 解析地址信息（从shopAddress中提取省市区信息）
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 创建商家账户
        $merchant = Merchant::create([
            'shop_name' => $data['shopName'],
            'phone' => $data['contactPhone'],
            'password' => Hash::make('123456'),
            'province' => $addressInfo['province'],
            'city' => $addressInfo['city'],
            'district' => $addressInfo['district'],
            'city_code' => $addressInfo['city_code'],
            'address' => $data['shopAddress'],
            'contact_name' => '',
            'email' => '',
            'merchant_type' => 'haibo',
            'status' => 1, // 海博商家默认审核通过
            'balance' => 0,
            'user_id' => $user->id,
        ]);

        // 生成配送商门店ID
        $carrierShopId = 'HB_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $user->id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'create'
        ];
    }

    /**
     * 更新现有门店
     *
     * @param Merchant $merchant
     * @param array $data
     * @return array
     */
    private function updateExistingStore(Merchant $merchant, array $data): array
    {
        // 解析地址信息
        $addressInfo = $this->parseAddress($data['shopAddress']);

        // 更新商家信息
        $merchant->update([
            'shop_name' => $data['shopName'],
            'province' => $addressInfo['province'],
            'city' => $addressInfo['city'],
            'district' => $addressInfo['district'],
            'city_code' => $addressInfo['city_code'],
            'address' => $data['shopAddress'],
        ]);


        // 生成配送商门店ID
        $carrierShopId = 'HB_' . $merchant->id;

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $merchant->user_id,
            'carrier_shop_id' => $carrierShopId,
            'operation' => 'update'
        ];
    }


    /**
     * 解析地址信息，从详细地址中提取省市区信息
     *
     * @param string $address
     * @return array
     */
    private function parseAddress(string $address): array
    {
        // 简单的地址解析逻辑，实际项目中可能需要更复杂的解析
        // 这里提供一个基础的解析示例

        $province = '';
        $city = '';
        $district = '';
        $cityCode = '';

        // 常见的省份匹配
        if (preg_match('/(北京|天津|上海|重庆)/', $address, $matches)) {
            $province = $matches[1] . '市';
            $city = $matches[1] . '市';
            $cityCode = $this->getCityCode($province);
        } elseif (preg_match('/(.+?省)(.+?市)(.+?[区县])/', $address, $matches)) {
            $province = $matches[1];
            $city = $matches[2];
            $district = $matches[3];
            $cityCode = $this->getCityCode($city);
        } elseif (preg_match('/(.+?市)(.+?[区县])/', $address, $matches)) {
            // 处理直辖市的情况
            $city = $matches[1];
            $district = $matches[2];
            if (in_array(substr($city, 0, 2), ['北京', '天津', '上海', '重庆'])) {
                $province = $city;
            }
            $cityCode = $this->getCityCode($city);
        }

        // 如果解析失败，使用默认值
        if (empty($province)) {
            $province = '浙江省';
            $city = '杭州市';
            $district = '余杭区';
            $cityCode = '330100';
        }

        return [
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'city_code' => $cityCode
        ];
    }

    /**
     * 获取城市编码（简化版本）
     *
     * @param string $cityName
     * @return string
     */
    private function getCityCode(string $cityName): string
    {
        $cityCodes = [
            '北京市' => '110100',
            '天津市' => '120100',
            '上海市' => '310100',
            '重庆市' => '500100',
            '杭州市' => '330100',
            '广州市' => '440100',
            '深圳市' => '440300',
            '成都市' => '510100',
            '武汉市' => '420100',
            '西安市' => '610100',
        ];

        return $cityCodes[$cityName] ?? '330100'; // 默认杭州
    }
}
